import db from "../../../db";
import { advocates } from "../../../db/schema";
import { advocateData } from "../../../db/seed/advocates";
import { sql } from "drizzle-orm";
import { PostgresJsDatabase } from "drizzle-orm/postgres-js";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '10');
  const search = searchParams.get('search') || '';
  const sortField = searchParams.get('sortField') || 'firstName';
  const sortOrder = searchParams.get('sortOrder') || 'asc';
  const offset = (page - 1) * limit;
  
  let data;
  let totalCount;
  
  if (process.env.DATABASE_URL) {
    // Create query builder
    let query = db.select().from(advocates);
    
    // Add search condition if search term provided
    if (search) {
      query = query.where(
        sql`LOWER(first_name) LIKE ${`%${search.toLowerCase()}%`} OR 
            LOWER(last_name) LIKE ${`%${search.toLowerCase()}%`} OR
            LOWER(city) LIKE ${`%${search.toLowerCase()}%`} OR
            LOWER(degree) LIKE ${`%${search.toLowerCase()}%`}`
      );
    }
    
    // Add sorting
    const dbSortField = camelToSnakeCase(sortField); // Convert camelCase to snake_case for DB
    query = query.orderBy(
      sortOrder === 'asc' 
        ? sql`${sql.raw(dbSortField)} ASC` 
        : sql`${sql.raw(dbSortField)} DESC`
    );
    
    // Execute query with pagination
    data = await query.limit(limit).offset(offset);
    
    // Get total count for pagination metadata
    let countQuery = db.select({ count: sql`count(*)`.mapWith(Number) }).from(advocates);
    if (search) {
      countQuery = countQuery.where(
        sql`LOWER(first_name) LIKE ${`%${search.toLowerCase()}%`} OR 
            LOWER(last_name) LIKE ${`%${search.toLowerCase()}%`} OR
            LOWER(city) LIKE ${`%${search.toLowerCase()}%`} OR
            LOWER(degree) LIKE ${`%${search.toLowerCase()}%`}`
      );
    }
    const countResult = await countQuery;
    totalCount = countResult[0]?.count || 0;
  } else {
    // Fallback to static data with client-side filtering and sorting
    let filteredData = [...advocateData];
    
    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filteredData = filteredData.filter(advocate => 
        advocate.firstName.toLowerCase().includes(searchLower) ||
        advocate.lastName.toLowerCase().includes(searchLower) ||
        advocate.city.toLowerCase().includes(searchLower) ||
        advocate.degree.toLowerCase().includes(searchLower) ||
        advocate.specialties.some(s => s.toLowerCase().includes(searchLower)) ||
        String(advocate.yearsOfExperience).includes(searchLower)
      );
    }
    
    // Apply sorting
    filteredData.sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortOrder === 'asc' 
          ? aValue.localeCompare(bValue) 
          : bValue.localeCompare(aValue);
      }
      
      return sortOrder === 'asc' 
        ? (aValue > bValue ? 1 : -1) 
        : (aValue < bValue ? 1 : -1);
    });
    
    totalCount = filteredData.length;
    data = filteredData.slice(offset, offset + limit);
  }

  return Response.json({ 
    data,
    pagination: {
      total: totalCount,
      page,
      limit,
      totalPages: Math.ceil(totalCount / limit)
    }
  });
}

// Helper function to convert camelCase to snake_case
function camelToSnakeCase(str) {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
}
