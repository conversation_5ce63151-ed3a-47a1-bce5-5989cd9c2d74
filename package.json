{"name": "solace-candidate-assignment", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "generate": "drizzle-kit generate", "migrate:up": "node ./src/db/migrate.js", "seed": "node --loader esbuild-register/loader -r esbuild-register ./src/db/seed/index.ts"}, "dependencies": {"drizzle-orm": "^0.32.1", "next": "^14.2.19", "postgres": "^3.4.4", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@types/node": "^20.14.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "drizzle-kit": "^0.23.0", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "postcss": "^8.4.40", "tailwindcss": "^3.4.7", "typescript": "^5.5.4"}}